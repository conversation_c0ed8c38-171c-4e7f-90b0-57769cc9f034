(defvar traduccion "")

(defwindow translate
  :geometry (geometry :x "50%" :y "10%" :width "500px" :height "300px")
  :stacking "fg"
  :exclusive false
  :focusable false
  (box
    :class "translate-box"
    :orientation "vertical"
    :spacing 15
    :halign "fill"
    :valign "start"
    (label
      :class "translate-title"
      :text "🌐 Traducción"
      :halign "center")
    (label
      :class "translate-text"
      :text traduccion
      :wrap true
      :halign "start"
      :valign "start")
  )
)
