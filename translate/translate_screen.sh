# importar lo necesario
## traducir la pantalla mediante captura,  usando herramientas como maim
##

# Variables
dirSalida="$HOME/scripts/translate"
lenguajeOrigen="es"
lenguajeDestino="en"
# Mapear a idioma de Tesseract
case "$lenguajeOrigen" in
  es) langTesseract="spa" ;;
  en) langTesseract="eng" ;;
  fr) langTesseract="fra" ;;
  de) langTesseract="deu" ;;
  it) langTesseract="ita" ;;
  pt) langTesseract="por" ;;
  *) langTesseract="eng" ;;
esac

# capturar region de la pantalla y guardar
hyprshot -m region -o "$dirSalida" -f texto.png --silent

# convertir a texto y luego traducir 
traduccion=$(tesseract "$dirSalida/texto.png" stdout -l "$langTesseract" 2>/dev/null | trans "$lenguajeOrigen:$lenguajeDestino" -brief)

echo "$traduccion"

