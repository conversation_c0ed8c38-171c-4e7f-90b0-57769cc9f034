#!/bin/bash

lenguajeOrigen="es"
lenguajeDestino="en"

# Mapear a Tesseract
case "$lenguajeOrigen" in
  es) langTesseract="spa" ;;
  en) langTesseract="eng" ;;
  fr) langTesseract="fra" ;;
  de) langTesseract="deu" ;;
  it) langTesseract="ita" ;;
  pt) langTesseract="por" ;;
  *) langTesseract="eng" ;;
esac

# Ejecutar OCR y traducción
TRADUCCION=$(hyprshot -m region | tesseract png:- stdout -l "$langTesseract" 2>/dev/null | trans "$lenguajeOrigen:$lenguajeDestino" -brief)

# Exportar la variable para EWW
export TRADUCCION

# Mostrar con eww
eww open translate-popup
sleep 5
eww close translate-popup
