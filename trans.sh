#!/bin/bash

lenguajeOrigen="es"
lenguajeDestino="en"

# Mapear a Tesseract
case "$lenguajeOrigen" in
  es) langTesseract="spa" ;;
  en) langTesseract="eng" ;;
  fr) langTesseract="fra" ;;
  de) langTesseract="deu" ;;
  it) langTesseract="ita" ;;
  pt) langTesseract="por" ;;
  *) langTesseract="eng" ;;
esac

# Mostrar ventana con mensaje de carga
eww update traduccion="Capturando y traduciendo..."
eww open translate

# Ejecutar OCR y traducción
TRADUCCION=$(hyprshot -m region | tesseract png:- stdout -l "$langTesseract" 2>/dev/null | trans "$lenguajeOrigen:$lenguajeDestino" -brief)

# Verificar si la traducción está vacía
if [ -z "$TRADUCCION" ]; then
    TRADUCCION="Error: No se pudo traducir el texto"
fi

# Actualizar la variable en EWW con la traducción
eww update traduccion="$TRADUCCION"

# Esperar y cerrar
sleep 5
eww close translate
